# Dog MCP Server

A Model Context Protocol (MCP) server that provides access to The Dog API, allowing you to fetch random dog images, search for specific breeds, and get detailed breed information.

## Features

- **Random Dog Images**: Get random dog images with optional breed filtering
- **Breed Search**: Search and list dog breeds with detailed information
- **Image Search**: Find images by specific breed name
- **API Status Check**: Check the connection and configuration status
- **Breed Information**: Get detailed breed characteristics and temperament

## Setup

### Environment Variables

Create a `.env` file in the project root:

```
DOG_API_KEY=your_api_key_here
```

Get your free API key from [The Dog API](https://thedogapi.com).

### Installation

#### Option 1: Direct Python

```bash
pip install -r requirements.txt
python dog_mcp_server.py
```

#### Option 2: Docker

```bash
docker build -t dog-mcp-server .
docker run -it dog-mcp-server
```

## Available Tools

- `get_random_dog_image()` - Get random dog images
- `get_dog_breeds()` - List dog breeds with filtering
- `search_dog_images()` - Search images by breed name
- `check_dog_api_status()` - Check API connectivity

## Resources

- `config://dog-api` - API configuration information
- `data://popular-breeds` - List of popular dog breeds

## Requirements

- Python 3.11+
- fastmcp
- httpx
- python-dotenv
