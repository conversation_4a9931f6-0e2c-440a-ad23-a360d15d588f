TITLE: Generating Text Responses with Mastra Agent
DESCRIPTION: This example shows how to use the `.generate()` method of a Mastra agent to produce a single text response. It takes an array of message objects (e.g., user input) and logs the agent's generated text output.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/agents/overview.mdx#_snippet_4

LANGUAGE: ts
CODE:
```
const response = await myAgent.generate([
  { role: "user", content: "Hello, how can you assist me today?" },
]);

console.log("Agent:", response.text);
```

----------------------------------------

TITLE: Defining a Workflow Step in TypeScript
DESCRIPTION: This TypeScript example illustrates the creation of a `Step` using the `createStep` function. It defines the step's unique ID, specifies input, output, resume, and suspend data schemas using Zod, and provides an asynchronous `execute` function to handle the step's core logic, processing `inputData` and returning a structured result.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/reference/workflows/step.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
import { createStep } from "@mastra/core/workflows";
import { z } from "zod";

const processOrder = createStep({
  id: "processOrder",
  inputSchema: z.object({
    orderId: z.string(),
    userId: z.string()
  }),
  outputSchema: z.object({
    status: z.string(),
    orderId: z.string()
  }),
  resumeSchema: z.object({
    orderId: z.string()
  }),
  suspendSchema: z.object({}),
  execute: async ({
    inputData,
    mastra,
    getStepResult,
    getInitData,
    suspend
  }) => {
    return {
      status: "processed",
      orderId: inputData.orderId
    };
  }
});
```

----------------------------------------

TITLE: Initializing and Running a Mastra Workflow
DESCRIPTION: This snippet demonstrates the creation and initialization of a Mastra workflow using `createWorkflow`, defining its ID, input/output schemas, and sequential steps. It then shows how to register the workflow with a `Mastra` instance and initiate a new workflow run using `createRun()`.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/reference/workflows/workflow.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
const myWorkflow = createWorkflow({
  id: "my-workflow",
  inputSchema: z.object({
    startValue: z.string(),
  }),
  outputSchema: z.object({
    result: z.string(),
  }),
  steps: [step1, step2, step3], // Declare steps used in this workflow
})
  .then(step1)
  .then(step2)
  .then(step3)
  .commit();

const mastra = new Mastra({
  workflows: {
    myWorkflow,
  },
});

const run = mastra.getWorkflow("myWorkflow").createRun();
```

----------------------------------------

TITLE: Creating a Basic Weather Tool with Mastra and Zod (TypeScript)
DESCRIPTION: This example demonstrates the basic usage of the `createTool` function to define a custom tool in Mastra. It shows how to import necessary modules, define a unique ID, specify an input schema using Zod, provide a description, and implement the tool's logic within the `execute` function. The tool is designed to fetch weather information for a given city.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/reference/tools/create-tool.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { createTool } from "@mastra/core/tools";
import { z } from "zod";

export const weatherInfo = createTool({
  id: "Get Weather Information",
  inputSchema: z.object({
    city: z.string(),
  }),
  description: `Fetches the current weather information for a given city`,
  execute: async ({ context: { city } }) => {
    // Tool logic here (e.g., API call)
    console.log("Using tool to fetch weather information for", city);
    return { temperature: 20, conditions: "Sunny" }; // Example return
  },
});
```

----------------------------------------

TITLE: Initializing Mastra and Executing Agent (TypeScript)
DESCRIPTION: This final snippet initializes the main `Mastra` instance, registering the `weatherWorkflow` and `activityPlannerAgent`. It then demonstrates how to invoke the `activityPlannerAgent` with a query, 'What activities do you recommend for a visit to Tokyo?', and prints the agent's generated response to the console. This showcases the end-to-end execution of the defined system.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/workflows/using-with-agents-and-tools.mdx#_snippet_8

LANGUAGE: TypeScript
CODE:
```
const mastra = new Mastra({
  workflows: {
    weatherWorkflow
  },
  agents: {
    activityPlannerAgent
  }
});

const response = await activityPlannerAgent.generate("What activities do you recommend for a visit to Tokyo?");

console.log("\nAgent response:");
console.log(response.text);
```

----------------------------------------

TITLE: Filtering Retrieval Results with Multiple Metadata Conditions (TypeScript)
DESCRIPTION: This snippet illustrates combining multiple metadata conditions (equality and numeric comparison) to refine search results. It retrieves chunks that belong to the 'electronics' category, have a price less than 1000, and are explicitly marked as 'inStock' true, allowing for precise multi-attribute filtering.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/rag/retrieval.mdx#_snippet_4

LANGUAGE: ts
CODE:
```
// Multiple conditions
const results = await pgVector.query({
  indexName: "embeddings",
  queryVector: embedding,
  topK: 10,
  filter: {
    category: "electronics",
    price: { $lt: 1000 },
    inStock: true
  }
});
```

----------------------------------------

TITLE: Initializing Voice Agent with OpenAI TTS in Mastra (TypeScript)
DESCRIPTION: This snippet demonstrates how to initialize a Mastra `Agent` with OpenAI voice capabilities for Text-to-Speech (TTS). It imports necessary modules, sets up the agent's name and instructions, specifies the `gpt-4o` model, and integrates `OpenAIVoice` for voice interactions. This setup enables the agent to function as a voice assistant.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/voice/overview.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";
import { OpenAIVoice } from "@mastra/voice-openai";

// Initialize OpenAI voice for TTS

const voiceAgent = new Agent({
  name: "Voice Agent",
  instructions:
    "You are a voice assistant that can help users with their tasks.",
  model: openai("gpt-4o"),
  voice: new OpenAIVoice()
});
```

----------------------------------------

TITLE: Defining a Suspendable Human-in-the-Loop Travel Workflow in TypeScript
DESCRIPTION: This snippet defines a `travelAgentWorkflow` using `@mastra/core/workflows` that orchestrates a travel planning process. It includes three steps: `generateSuggestionsStep` (AI-driven suggestions), `humanInputStep` (pauses for user selection), and `travelPlannerStep` (AI-driven detailed plan). The `humanInputStep` demonstrates Mastra's suspend/resume mechanism for interactive workflows.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/workflows/human-in-the-loop.mdx#_snippet_2

LANGUAGE: TypeScript
CODE:
```
import { createWorkflow, createStep } from '@mastra/core/workflows'
import { z } from 'zod'
 
// Step that generates multiple holiday options based on user's vacation description
// Uses the summaryTravelAgent to create diverse travel suggestions
const generateSuggestionsStep = createStep({
  id: "generate-suggestions",
  inputSchema: z.object({
    vacationDescription: z.string().describe("The description of the vacation"),
  }),
  outputSchema: z.object({
    suggestions: z.array(z.string()),
    vacationDescription: z.string(),
  }),
  execute: async ({ inputData, mastra }) => {
    if (!mastra) {
      throw new Error("Mastra is not initialized");
    }
 
    const { vacationDescription } = inputData
    const result = await mastra.getAgent('summaryTravelAgent').generate([
      {
        role: "user",
        content: vacationDescription,
      },
    ]);
    console.log(result.text);
    return { suggestions: JSON.parse(result.text), vacationDescription };
  },
})
 
// Step that pauses the workflow to get user input
// Allows the user to select their preferred holiday option from the suggestions
// Uses suspend/resume mechanism to handle the interaction
const humanInputStep = createStep({
  id: "human-input",
  inputSchema: z.object({
    suggestions: z.array(z.string()),
    vacationDescription: z.string(),
  }),
  outputSchema: z.object({
    selection: z.string().describe("The selection of the user"),
    vacationDescription: z.string(),
  }),
  resumeSchema: z.object({
    selection: z.string().describe("The selection of the user"),
  }),
  suspendSchema: z.object({
    suggestions: z.array(z.string()),
  }),
  execute: async ({ inputData, resumeData, suspend, getInitData }) => {
    if (!resumeData?.selection) {
      await suspend({ suggestions: inputData?.suggestions });
      return {
        selection: "",
        vacationDescription: inputData?.vacationDescription,
      };
    }
 
    return {
      selection: resumeData?.selection,
      vacationDescription: inputData?.vacationDescription,
    };
  },
})
 
// Step that creates a detailed travel plan based on the user's selection
// Uses the travelAgent to generate comprehensive holiday details
const travelPlannerStep = createStep({
  id: "travel-planner",
  inputSchema: z.object({
    selection: z.string().describe("The selection of the user"),
    vacationDescription: z.string(),
  }),
  outputSchema: z.object({
    travelPlan: z.string(),
  }),
  execute: async ({ inputData, mastra }) => {
    const travelAgent = mastra?.getAgent("travelAgent");
    if (!travelAgent) {
      throw new Error("Travel agent is not initialized");
    }
 
    const { selection, vacationDescription } = inputData
    const result = await travelAgent.generate([
      { role: "assistant", content: vacationDescription },
      { role: "user", content: selection || "" },
    ]);
    console.log(result.text);
    return { travelPlan: result.text };
  },
})
 
// Main workflow that orchestrates the holiday planning process:
// 1. Generates multiple options
// 2. Gets user input
// 3. Creates detailed plan
const travelAgentWorkflow = createWorkflow({
  id: "travel-agent-workflow",
  inputSchema: z.object({
    vacationDescription: z.string().describe("The description of the vacation"),
  }),
  outputSchema: z.object({
    travelPlan: z.string(),
  }),
})
  .then(generateSuggestionsStep)
  .then(humanInputStep)
  .then(travelPlannerStep)
 
travelAgentWorkflow.commit()
 
export { travelAgentWorkflow, humanInputStep }
```

----------------------------------------

TITLE: Setting OpenAI API Key in .env (Bash)
DESCRIPTION: This snippet shows how to add the `OPENAI_API_KEY` to the `.env` file. This environment variable is crucial for authenticating with the OpenAI API, which is used by the Mastra agent for LLM interactions.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/guides/guide/ai-recruiter.mdx#_snippet_1

LANGUAGE: Bash
CODE:
```
OPENAI_API_KEY=<your-openai-key>
```

----------------------------------------

TITLE: Configuring Anthropic and OpenAI API Keys (Environment)
DESCRIPTION: This snippet shows how to set the `ANTHROPIC_API_KEY` and `OPENAI_API_KEY` in the `.env` file. These keys are essential for the agents to authenticate with the respective AI services.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/examples/basics/agents/hierarchical-multi-agent/README.md#_snippet_2

LANGUAGE: env
CODE:
```
ANTHROPIC_API_KEY=sk-your-api-key-here
OPENAI_API_KEY=sk-your-api-key-here
```

----------------------------------------

TITLE: Configuring Environment Variables - ENV
DESCRIPTION: This snippet illustrates the content of the `.env` file, showing placeholders for the OpenAI API key and PostgreSQL connection string. These variables are essential for the application to authenticate with OpenAI and connect to the database.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/examples/basics/rag/cot-workflow-rag/README.md#_snippet_2

LANGUAGE: env
CODE:
```
OPENAI_API_KEY=sk-your-api-key-here
POSTGRES_CONNECTION_STRING=your-postgres-connection-string-here
```

----------------------------------------

TITLE: Streaming Agent Response with Working Memory (TypeScript)
DESCRIPTION: This snippet illustrates how to interact with the configured agent using `stream` to get a response. It generates unique `threadId` and `resourceId` for the interaction, sends a message, and then processes the streamed text chunks, demonstrating how the agent remembers details like the user's name.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/memory/streaming-working-memory.mdx#_snippet_2

LANGUAGE: typescript
CODE:
```
import { randomUUID } from "crypto";

const threadId = randomUUID();
const resourceId = "SOME_USER_ID";

const response = await agent.stream("Hello, my name is Jane", {
  threadId,
  resourceId,
});

for await (const chunk of response.textStream) {
  process.stdout.write(chunk);
}
```

----------------------------------------

TITLE: Configuring and Executing Multi-Agent Workflow (TypeScript)
DESCRIPTION: This snippet configures the main workflow using `createWorkflow`, defining its overall input (`topic`) and output (`finalCopy`) schemas. It then chains the `copywriterStep` and `editorStep` sequentially using `.then()` and `commit()`. Finally, it creates and starts a workflow run with a sample topic, demonstrating the end-to-end execution of the multi-agent process.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/agents/multi-agent-workflow.mdx#_snippet_5

LANGUAGE: typescript
CODE:
```
const myWorkflow = createWorkflow({
  id: 'my-workflow',
  inputSchema: z.object({
    topic: z.string()
  }),
  outputSchema: z.object({
    finalCopy: z.string()
  })
});

// Run steps sequentially.
myWorkflow.then(copywriterStep).then(editorStep).commit();

const run = myWorkflow.createRun();

const res = await run.start({ inputData: { topic: 'React JavaScript frameworks' } });
console.log('Response: ', res);
```

----------------------------------------

TITLE: Initializing a Mastra Agent in TypeScript
DESCRIPTION: This snippet demonstrates how to create a new agent instance in Mastra using the `Agent` class. It defines the agent's name, initial instructions, and the language model to be used, such as OpenAI's `gpt-4o-mini`. This requires the `@mastra/core` package and a configured OpenAI API key.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/agents/overview.mdx#_snippet_0

LANGUAGE: ts
CODE:
```
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";

export const myAgent = new Agent({
  name: "My Agent",
  instructions: "You are a helpful assistant.",
  model: openai("gpt-4o-mini"),
});
```

----------------------------------------

TITLE: Generating and Storing Embeddings in PgVector (TypeScript)
DESCRIPTION: This snippet generates vector embeddings for each text chunk using OpenAI's `text-embedding-3-small` model. It then retrieves the `pgVector` store instance from Mastra, creates a new index named 'papers' with a dimension of 1536, and finally upserts the generated embeddings along with their original text and source metadata into the vector database for efficient semantic search.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/guides/guide/research-assistant.mdx#_snippet_4

LANGUAGE: typescript
CODE:
```
// Generate embeddings
const { embeddings } = await embedMany({
  model: openai.embedding("text-embedding-3-small"),
  values: chunks.map((chunk) => chunk.text),
});

// Get the vector store instance from Mastra
const vectorStore = mastra.getVector("pgVector");

// Create an index for our paper chunks
await vectorStore.createIndex({
  indexName: "papers",
  dimension: 1536,
});

// Store embeddings
await vectorStore.upsert({
  indexName: "papers",
  vectors: embeddings,
  metadata: chunks.map((chunk) => ({
    text: chunk.text,
    source: "transformer-paper",
  })),
});
```

----------------------------------------

TITLE: Defining a Weather-Based Planning Agent (TypeScript)
DESCRIPTION: This TypeScript snippet defines `planningAgent`, an AI agent utilizing `@mastra/core` and the `gpt-4o` model from `@ai-sdk/openai`. Its primary function is to act as a weather-based travel expert, providing structured activity recommendations (morning, afternoon, indoor alternatives) based on weather conditions, adhering to a strict output format.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/workflows/parallel-steps.mdx#_snippet_1

LANGUAGE: ts
CODE:
```
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";

const llm = openai("gpt-4o");

// Define the planning agent with specific instructions for formatting
// and structuring weather-based activity recommendations
const planningAgent = new Agent({
  name: "planningAgent",
  model: llm,
  instructions: `
        You are a local activities and travel expert who excels at weather-based planning. Analyze the weather data and provide practical activity recommendations.

        📅 [Day, Month Date, Year]
        ═══════════════════════════

        🌡️ WEATHER SUMMARY
        • Conditions: [brief description]
        • Temperature: [X°C/Y°F to A°C/B°F]
        • Precipitation: [X% chance]

        🌅 MORNING ACTIVITIES
        Outdoor:
        • [Activity Name] - [Brief description including specific location/route]
          Best timing: [specific time range]
          Note: [relevant weather consideration]

        🌞 AFTERNOON ACTIVITIES
        Outdoor:
        • [Activity Name] - [Brief description including specific location/route]
          Best timing: [specific time range]
          Note: [relevant weather consideration]

        🏠 INDOOR ALTERNATIVES
        • [Activity Name] - [Brief description including specific venue]
          Ideal for: [weather condition that would trigger this alternative]

        ⚠️ SPECIAL CONSIDERATIONS
        • [Any relevant weather warnings, UV index, wind conditions, etc.]

        Guidelines:
        - Suggest 2-3 time-specific outdoor activities per day
        - Include 1-2 indoor backup options
        - For precipitation >50%, lead with indoor activities
        - All activities must be specific to the location
        - Include specific venues, trails, or locations
        - Consider activity intensity based on temperature
        - Keep descriptions concise but informative

        Maintain this exact formatting for consistency, using the emoji and section headers as shown.
      `,
});

export { planningAgent };
```

----------------------------------------

TITLE: Configuring Dynamic Toolsets with MCPClient in TypeScript
DESCRIPTION: This snippet demonstrates how to create an `Agent` without initial tools and then dynamically configure `MCPClient` with user-specific server settings (e.g., API keys, authorization tokens). It shows how to pass the dynamically obtained toolsets from `mcp.getToolsets()` to the `agent.stream()` method, enabling the agent to use user-specific tools for tasks like checking stock prices and weather.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/reference/tools/mcp-client.mdx#_snippet_20

LANGUAGE: typescript
CODE:
```
import { Agent } from "@mastra/core/agent";
import { MCPClient } from "@mastra/mcp";
import { openai } from "@ai-sdk/openai";

// Create the agent first, without any tools
const agent = new Agent({
  name: "Multi-tool Agent",
  instructions: "You help users check stocks and weather.",
  model: openai("gpt-4"),
});

// Later, configure MCP with user-specific settings
const mcp = new MCPClient({
  servers: {
    stockPrice: {
      command: "npx",
      args: ["tsx", "stock-price.ts"],
      env: {
        API_KEY: "user-123-api-key",
      },
      timeout: 20000, // Server-specific timeout
    },
    weather: {
      url: new URL("http://localhost:8080/sse"),
      requestInit: {
        headers: {
          Authorization: `Bearer user-123-token`,
        },
      },
    },
  },
});

// Pass all toolsets to stream() or generate()
const response = await agent.stream(
  "How is AAPL doing and what is the weather?",
  {
    toolsets: await mcp.getToolsets(),
  },
);
```

----------------------------------------

TITLE: Upserting Embeddings into Qdrant (TypeScript)
DESCRIPTION: This snippet demonstrates how to use the `QdrantVector` class to create collections and insert embeddings into Qdrant, a high-performance vector database. It involves chunking text, generating embeddings with OpenAI (including a `maxRetries` option), and then upserting these vectors and their metadata into a specified Qdrant collection. Both `url` and `apiKey` are required for Qdrant connection.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/rag/upsert/upsert-embeddings.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
import { openai } from '@ai-sdk/openai';
import { QdrantVector } from '@mastra/qdrant';
import { MDocument } from '@mastra/rag';
import { embedMany } from 'ai';

const doc = MDocument.fromText('Your text content...');

const chunks = await doc.chunk();

const { embeddings } = await embedMany({
  values: chunks.map(chunk => chunk.text),
  model: openai.embedding('text-embedding-3-small'),
  maxRetries: 3,
});

const qdrant = new QdrantVector({
  url: process.env.QDRANT_URL,
  apiKey: process.env.QDRANT_API_KEY,
});

await qdrant.createIndex({
  indexName: 'test_collection',
  dimension: 1536,
});

await qdrant.upsert({
  indexName: 'test_collection',
  vectors: embeddings,
  metadata: chunks?.map(chunk => ({ text: chunk.text })),
});
```

----------------------------------------

TITLE: Implementing RAG Workflow with Mastra and TypeScript
DESCRIPTION: This snippet demonstrates a complete RAG workflow in TypeScript using Mastra components. It covers initializing a document, chunking its content, generating embeddings using OpenAI's `text-embedding-3-small` model, storing these embeddings in a PgVector database, and finally querying for similar content. Key dependencies include `@mastra/rag`, `@mastra/pg`, and `@ai-sdk/openai`. The `queryVector` would typically be the embedding of the user's query.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/rag/overview.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { embedMany } from "ai";
import { openai } from "@ai-sdk/openai";
import { PgVector } from "@mastra/pg";
import { MDocument } from "@mastra/rag";
import { z } from "zod";

// 1. Initialize document
const doc = MDocument.fromText(`Your document text here...`);

// 2. Create chunks
const chunks = await doc.chunk({
  strategy: "recursive",
  size: 512,
  overlap: 50,
});

// 3. Generate embeddings; we need to pass the text of each chunk
const { embeddings } = await embedMany({
  values: chunks.map((chunk) => chunk.text),
  model: openai.embedding("text-embedding-3-small"),
});

// 4. Store in vector database
const pgVector = new PgVector({ connectionString: process.env.POSTGRES_CONNECTION_STRING });
await pgVector.upsert({
  indexName: "embeddings",
  vectors: embeddings,
}); // using an index name of 'embeddings'

// 5. Query similar chunks
const results = await pgVector.query({
  indexName: "embeddings",
  queryVector: queryVector,
  topK: 3,
}); // queryVector is the embedding of the query

console.log("Similar chunks:", results);
```

----------------------------------------

TITLE: Configuring Dynamic Support Agent with Runtime Context in Mastra
DESCRIPTION: This code defines a `supportAgent` using Mastra's `Agent` class. It dynamically sets `instructions`, `model`, and `tools` based on the `runtimeContext`. Instructions adapt to user tier and language, the model switches between `gpt-4` and `gpt-3.5-turbo` based on tier, and tools are added conditionally for 'pro' and 'enterprise' users.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/agents/dynamic-agents.mdx#_snippet_1

LANGUAGE: TypeScript
CODE:
```
const supportAgent = new Agent({
  name: "Dynamic Support Agent",
  
  instructions: async ({ runtimeContext }) => {
    const userTier = runtimeContext.get("user-tier");
    const language = runtimeContext.get("language");
    
    return `You are a customer support agent for our SaaS platform.
    The current user is on the ${userTier} tier and prefers ${language} language.
    
    For ${userTier} tier users:
    ${userTier === "free" ? "- Provide basic support and documentation links" : ""}
    ${userTier === "pro" ? "- Offer detailed technical support and best practices" : ""}
    ${userTier === "enterprise" ? "- Provide priority support with custom solutions" : ""}
    
    Always respond in ${language} language.`;
  },

  model: ({ runtimeContext }) => {
    const userTier = runtimeContext.get("user-tier");
    return userTier === "enterprise" 
      ? openai("gpt-4") 
      : openai("gpt-3.5-turbo");
  },

  tools: ({ runtimeContext }) => {
    const userTier = runtimeContext.get("user-tier");
    const baseTools = [knowledgeBase, ticketSystem];
    
    if (userTier === "pro" || userTier === "enterprise") {
      baseTools.push(advancedAnalytics);
    }
    
    if (userTier === "enterprise") {
      baseTools.push(customIntegration);
    }
    
    return baseTools;
  }
});
```

----------------------------------------

TITLE: Creating a User Interaction Step with Suspend/Resume (TypeScript)
DESCRIPTION: Defines a `createStep` that handles user interaction by suspending the workflow until input is provided. It uses `resumeSchema` for expected resume data and `suspendSchema` for context during suspension.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/packages/core/src/workflows/README.md#_snippet_16

LANGUAGE: typescript
CODE:
```
const userInputStep = createStep({
  id: 'get-user-input',
  inputSchema: z.object({}),
  resumeSchema: z.object({
    userSelection: z.string(),
  }),
  suspendSchema: z.object({
    suspendContext: z.string(),
  }),
  outputSchema: z.object({
    userSelection: z.string(),
  }),
  execute: async ({ resumeData, suspend }) => {
    if (!resumeData?.userSelection) {
      // Suspend the workflow until user provides input
      await suspend({
        suspendContext: 'Waiting for user selection',
      });
      return { userSelection: '' }; // This return is not used when suspended
    }
    // If userSelection exists, continue with it
    return { userSelection: resumeData.userSelection };
  },
});
```

----------------------------------------

TITLE: Executing and Resuming Human-in-the-Loop Workflow (TypeScript)
DESCRIPTION: This TypeScript file demonstrates how to execute and resume the human-in-the-loop workflow. It retrieves the `travelAgentWorkflow` from the Mastra instance, starts a new run with an initial vacation description, and then pauses. After the `generate-suggestions` step completes, it uses `@inquirer/prompts` to collect user input for selecting a holiday destination. Finally, it resumes the workflow at the `humanInputStep` with the user's selection, allowing the process to continue.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/ja/examples/workflows_vNext/human-in-the-loop.mdx#_snippet_4

LANGUAGE: ts
CODE:
```
import { mastra } from "./"
import { select } from '@inquirer/prompts'
import { humanInputStep } from './workflows/human-in-the-loop-workflow'
 

const workflow = mastra.vnext_getWorkflow('travelAgentWorkflow')
const run = workflow.createRun({})

// Start the workflow with initial vacation description
const result = await run.start({
  inputData: { vacationDescription: 'I want to go to the beach' }
})
 
console.log('result', result)
 
const suggStep = result?.steps?.['generate-suggestions']
 
// If suggestions were generated successfully, proceed with user interaction
if (suggStep.status === 'success') {
  const suggestions = suggStep.output?.suggestions
  
  // Present options to user and get their selection
  const userInput = await select<string>({
    message: "Choose your holiday destination",
    choices: suggestions.map(({ location, description }: { location: string, description: string }) => `- ${location}: ${description}`)
  })
 
  console.log('Selected:', userInput)
 
  // Prepare to resume the workflow with user's selection
  console.log('resuming from', result, 'with', {
    inputData: {
      selection: userInput,
      vacationDescription: "I want to go to the beach",
      suggestions: suggStep?.output?.suggestions
    },
    step: humanInputStep
  })

  const result2 = await run.resume({
    resumeData: {
      selection: userInput
    },
    step: humanInputStep
  })
 
  console.dir(result2, { depth: null })
}
```

----------------------------------------

TITLE: Creating a Human Input Step with Suspend/Resume in Mastra (TypeScript)
DESCRIPTION: This snippet demonstrates how to define a workflow step using `createStep` that can suspend execution, waiting for human input before resuming. It specifies the `inputSchema`, `resumeSchema` (for data needed to resume), and `suspendSchema`. The `execute` function checks for `resumeData`; if absent, it calls `suspend({})` to pause the workflow, otherwise it processes the `resumeData`.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/workflows/suspend-and-resume.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
// Create a step that handles human input with suspend/resume capabilities
const humanInputStep = createStep({
  id: "human-input",
  inputSchema: z.object({
    suggestions: z.array(z.string()),
    vacationDescription: z.string(),
  }),
  // Define the structure of data needed to resume the step
  resumeSchema: z.object({
    selection: z.string(),
  }),
  // Define the structure of data when suspending (empty in this case)
  suspendSchema: z.object({}),
  outputSchema: z.object({
    selection: z.string().describe("The selection of the user"),
    vacationDescription: z.string(),
  }),
  execute: async ({ inputData, resumeData, suspend }) => {
    // If no resume data is provided, suspend the step and wait for user input
    if (!resumeData?.selection) {
      await suspend({});
      return {
        selection: "",
        vacationDescription: inputData?.vacationDescription,
      };
    }
    return {
      selection: resumeData.selection,
      vacationDescription: inputData?.vacationDescription,
    };
  },
});
```

----------------------------------------

TITLE: Implementing Conditional Branching - vNext vs. Original Mastra API (TypeScript)
DESCRIPTION: Compares conditional logic. vNext uses an array-based `branch` method with predicate functions, offering a clearer and more explicit decision path than the original API's `when` conditions within `then`.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/packages/core/src/workflows/README.md#_snippet_32

LANGUAGE: typescript
CODE:
```
// vNext - array-based branching
workflow.branch([
  [async ({ inputData }) => inputData.value > 50, highValueStep],
  [async ({ inputData }) => inputData.value <= 50, lowValueStep],
]);
```

LANGUAGE: typescript
CODE:
```
// Original Mastra API - when-based conditions
workflow
  .then(step2, {
    id: 'step2',
    when: {
      ref: { step: step1, path: 'status' },
      query: { $eq: 'success' },
    },
  })
  .then(step3, {
    id: 'step3',
    when: {
      ref: { step: step1, path: 'status' },
      query: { $eq: 'failed' },
    },
  });
```

----------------------------------------

TITLE: Mapping Variables Between Workflow Steps (TypeScript)
DESCRIPTION: Demonstrates the use of `.map()` to explicitly transform and pass data between steps. It shows how to map values from a previous step's output, runtime context, constant values, and initial workflow data.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/packages/core/src/workflows/README.md#_snippet_15

LANGUAGE: typescript
CODE:
```
myWorkflow
  .then(step1)
  .map({
    transformedValue: {
      step: step1,
      path: 'nestedValue',
    },
    runtimeContextValue: {
      runtimeContextPath: 'runtimeContextValue',
      schema: z.number(),
    },
    constantValue: {
      value: 42,
      schema: z.number(),
    },
    initDataValue: {
      initData: myWorkflow,
      path: 'startValue',
    },
  })
  .then(step2)
  .commit();
```

----------------------------------------

TITLE: Initializing an AI Agent with Mastra Core
DESCRIPTION: This snippet shows how to create an `Agent` instance using `@mastra/core/agent`. Agents are autonomous AI entities that can understand instructions, use tools, and complete tasks. It requires a name, instructions, and a model (e.g., OpenAI's gpt-4o-mini).
SOURCE: https://github.com/mastra-ai/mastra/blob/main/packages/core/README.md#_snippet_1

LANGUAGE: typescript
CODE:
```
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';

const agent = new Agent({
  name: 'my-agent',
  instructions: 'Your task-specific instructions',
  model: openai('gpt-4o-mini'),
  tools: {}, // Optional tools
});
```

----------------------------------------

TITLE: Performing Metadata-Based Queries with the RAG Agent (TypeScript)
DESCRIPTION: This snippet provides examples of how to query the RAG system using the configured agent. It demonstrates various types of queries, including semantic search, filtering by a nested ID field using a 'greater than' operator, and searching the 'text' field using a regex operator, showcasing the agent's ability to apply metadata filters effectively.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/rag/usage/filter-rag.mdx#_snippet_8

LANGUAGE: typescript
CODE:
```
const queryOne = "What are the adaptation strategies mentioned?";
const answerOne = await agent.generate(queryOne);
console.log("\nQuery:", queryOne);
console.log("Response:", answerOne.text);

const queryTwo =
  'Show me recent sections. Check the "nested.id" field and return values that are greater than 2.';
const answerTwo = await agent.generate(queryTwo);
console.log("\nQuery:", queryTwo);
console.log("Response:", answerTwo.text);

const queryThree =
  'Search the "text" field using regex operator to find sections containing "temperature".';
const answerThree = await agent.generate(queryThree);
console.log("\nQuery:", queryThree);
console.log("Response:", answerThree.text);
```

----------------------------------------

TITLE: Initializing VectorQueryTool with Filtering Enabled (TypeScript)
DESCRIPTION: This snippet initializes a `createVectorQueryTool` configured to enable metadata filtering. It connects to a Pinecone vector store, uses a specified index and embedding model, and sets `enableFilter` to `true` to allow agent-driven construction of metadata filters from natural language queries.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/reference/tools/vector-query-tool.mdx#_snippet_2

LANGUAGE: typescript
CODE:
```
const queryTool = createVectorQueryTool({
  vectorStoreName: "pinecone",
  indexName: "docs",
  model: openai.embedding("text-embedding-3-small"),
  enableFilter: true,
});
```

----------------------------------------

TITLE: Creating Custom Tools with Mastra Core (TypeScript)
DESCRIPTION: This snippet demonstrates how to define a custom tool using `@mastra/core/tools`. It shows the `createTool` function, specifying an `id`, an `inputSchema` using `zod` for input validation, a `description`, and an `execute` function containing the tool's logic. The `execute` function receives a `context` object with input parameters.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/agents/using-tools-and-mcp.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { createTool } from "@mastra/core/tools";
import { z } from "zod";

export const weatherInfo = createTool({
  id: "Get Weather Information",
  inputSchema: z.object({
    city: z.string(),
  }),
  description: `Fetches the current weather information for a given city`,
  execute: async ({ context: { city } }) => {
    // Tool logic here (e.g., API call)
    console.log("Using tool to fetch weather information for", city);
    return { temperature: 20, conditions: "Sunny" }; // Example return
  },
});
```

----------------------------------------

TITLE: Initializing Mastra Project with pnpm - Bash
DESCRIPTION: This command sequence initializes a new Node.js project using pnpm, installs essential development dependencies like TypeScript and tsx, and then adds core Mastra packages along with zod and @ai-sdk/openai for AI functionalities. This sets up the project structure and required libraries efficiently.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/getting-started/installation.mdx#_snippet_5

LANGUAGE: bash
CODE:
```
pnpm init

pnpm add typescript tsx @types/node mastra@latest --save-dev

pnpm add @mastra/core@latest zod @ai-sdk/openai
```

----------------------------------------

TITLE: Defining Prompt Intent in TypeScript
DESCRIPTION: Illustrates the importance of clear and specific intent when creating prompts using `createPrompt`. It contrasts a vague prompt with a clear one, emphasizing that the primary instruction should be precise for better model performance.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/explorations/prompt/prompt-template.md#_snippet_0

LANGUAGE: typescript
CODE:
```
// ❌ Vague intent
createPrompt('fix this');

// ✅ Clear intent
createPrompt('Add type checking to this function');
```

----------------------------------------

TITLE: Creating and Running a Tool-Based Workflow in Mastra (TypeScript)
DESCRIPTION: This snippet illustrates how to build and execute a Mastra workflow that utilizes a custom tool as a step. It involves defining a `createTool` instance for weather information, creating custom steps for input formatting and output formatting, integrating the tool using `createStep(tool)`, defining the workflow's structure, and running it with example input.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/workflows/using-with-agents-and-tools.mdx#_snippet_1

LANGUAGE: typescript
CODE:
```
import { createTool, Mastra } from "@mastra/core";
import { createWorkflow, createStep } from "@mastra/core/workflows";
import { z } from "zod";

// Create a weather tool
const weatherTool = createTool({
  id: "weather-tool",
  description: "Get weather information for a location",
  inputSchema: z.object({
      location: z.string().describe("The city name")
  }),
  outputSchema: z.object({
      temperature: z.number(),
      conditions: z.string()
  }),
  execute: async ({ context: {location} }) => {
      return { 
          temperature: 22, 
          conditions: "Sunny" 
      };
  },
});

// Create a step that formats the input
const locationStep = createStep({
  id: "location-formatter",
  inputSchema: z.object({
      city: z.string()
  }),
  outputSchema: z.object({
      location: z.string()
  }),
  execute: async ({ inputData }) => {
      return {
          location: inputData.city
      };
  }
});

// Create a step that formats the output
const formatResultStep = createStep({
  id: "format-result",
  inputSchema: z.object({
      temperature: z.number(),
      conditions: z.string()
  }),
  outputSchema: z.object({
      weatherReport: z.string()
  }),
  execute: async ({ inputData }) => {
      return {
          weatherReport: `Current weather: ${inputData.temperature}°C and ${inputData.conditions}`
      };
  }
});

const weatherToolStep = createStep(weatherTool)

// Create the workflow
const weatherWorkflow = createWorkflow({
  id: "weather-workflow",
  inputSchema: z.object({
      city: z.string()
  }),
  outputSchema: z.object({
      weatherReport: z.string()
  }),
  steps: [locationStep, weatherToolStep, formatResultStep]
});

// Define workflow sequence
weatherWorkflow
  .then(locationStep)
  .then(weatherToolStep)
  .then(formatResultStep)
  .commit();

// Create Mastra instance
const mastra = new Mastra({
  workflows: {
      weatherWorkflow
  }
});


const workflow = mastra.getWorkflow("weatherWorkflow");
const run = workflow.createRun();

// Run the workflow
const result = await run.start({
  inputData: {
      city: "Tokyo"
  }
});

if (result.status === "success") {
  console.log(result.result.weatherReport);
} else if (result.status === "failed") {
  console.error("Workflow failed:", result.error);
}
```

----------------------------------------

TITLE: Defining Activity Planning Step and Workflow in Mastra AI (TypeScript)
DESCRIPTION: This section defines two key components: the 'planActivities' step and the 'activityPlanningWorkflow'. The 'planActivities' step leverages a Mastra AI agent ('planningAgent') to generate activity recommendations based on the weather forecast provided as input. The 'activityPlanningWorkflow' orchestrates the execution, chaining 'fetchWeather' and 'planActivities' to provide a complete solution from city input to activity suggestions. It also includes the 'commit()' call to finalize the workflow definition.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/workflows/calling-agent.mdx#_snippet_4

LANGUAGE: TypeScript
CODE:
```
const planActivities = createStep({
  id: "plan-activities",
  description: "Suggests activities based on weather conditions",
  inputSchema: forecastSchema,
  outputSchema: z.object({
    activities: z.string(),
  }),
  execute: async ({ inputData, mastra }) => {
    const forecast = inputData
 
    if (!forecast) {
      throw new Error("Forecast data not found");
    }
 
    const prompt = `Based on the following weather forecast for ${forecast.location}, suggest appropriate activities:
      ${JSON.stringify(forecast, null, 2)}
      `
 
    const agent = mastra?.getAgent('planningAgent')
    if (!agent) {
      throw new Error("Planning agent not found");
    }
 
    const response = await agent.stream([
      {
        role: "user",
        content: prompt,
      },
    ])
 
    let activitiesText = ''
    for await (const chunk of response.textStream) {
      process.stdout.write(chunk);
      activitiesText += chunk;
    }
 
    return {
      activities: activitiesText,
    };
  },
})
 
const activityPlanningWorkflow = createWorkflow({
  steps: [fetchWeather, planActivities],
  id: 'activity-planning-step1-single-day',
  inputSchema: z.object({
    city: z.string().describe("The city to get the weather for"),
  }),
  outputSchema: z.object({
    activities: z.string(),
  }),
})
  .then(fetchWeather)
  .then(planActivities)
 
activityPlanningWorkflow.commit()
 
export { activityPlanningWorkflow }
```

----------------------------------------

TITLE: Using Mastra Agent Stream with Memory Context (TypeScript)
DESCRIPTION: This example illustrates how to interact with a Mastra agent's `stream()` method while leveraging its memory. By providing `resourceId` (identifying the user) and `threadId` (identifying the conversation), the agent can store and recall information, as shown by remembering and later recalling a user's favorite color within the same thread.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/agents/agent-memory.mdx#_snippet_2

LANGUAGE: typescript
CODE:
```
// Example agent call using memory
await agent.stream("Remember my favorite color is blue.", {
  resourceId: "user_alice",
  threadId: "preferences_thread"
});

// Later in the same thread...
const response = await agent.stream("What's my favorite color?", {
  resourceId: "user_alice",
  threadId: "preferences_thread"
});
// Agent will use memory to recall the favorite color.
```

----------------------------------------

TITLE: Instantiating PgVector and Mastra Core Components
DESCRIPTION: This code initializes the `PgVector` instance using the provided PostgreSQL connection string. It then creates the main `Mastra` instance, registering the `ragAgent`, `pgVector` store, and `ragWorkflow` to ensure all components are available and integrated within the Mastra ecosystem.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/rag/usage/cot-workflow-rag.mdx#_snippet_11

LANGUAGE: typescript
CODE:
```
const pgVector = new PgVector({ connectionString: process.env.POSTGRES_CONNECTION_STRING! });

export const mastra = new Mastra({
  agents: { ragAgent },
  vectors: { pgVector },
  workflows: { ragWorkflow }
});
```

----------------------------------------

TITLE: Merging Parallel Branches with Multiple Dependencies in Mastra Workflow (TypeScript)
DESCRIPTION: This snippet demonstrates merging multiple parallel execution paths using the '.after([])' syntax. 'fetchUserData' and 'fetchProductData' run in parallel with their respective validation steps, and 'processOrder' only executes after both 'validateUserData' and 'validateProductData' have successfully completed, ensuring synchronization.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/ja/docs/workflows/control-flow.mdx#_snippet_3

LANGUAGE: TypeScript
CODE:
```
myWorkflow
  .step(fetchUserData)
  .then(validateUserData)
  .step(fetchProductData)
  .then(validateProductData)
  // This step will only run after BOTH validateUserData AND validateProductData have completed
  .after([validateUserData, validateProductData])
  .step(processOrder);
```

----------------------------------------

TITLE: Defining Conditional Workflow with Mastra (TypeScript)
DESCRIPTION: This snippet defines `activityPlanningWorkflow` using `createWorkflow`, specifying input/output schemas. It uses `.then(fetchWeather)` for a sequential step and `.branch` for conditional execution, directing to `planIndoorActivities` or `planActivities` based on `precipitationChance`. The workflow is then committed and exported for use.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/workflows/conditional-branching.mdx#_snippet_6

LANGUAGE: TypeScript
CODE:
```
const activityPlanningWorkflow = createWorkflow({
  id: 'activity-planning-workflow-step2-if-else',
  inputSchema: z.object({
    city: z.string().describe("The city to get the weather for")
  }),
  outputSchema: z.object({
    activities: z.string()
  })
})
  .then(fetchWeather)
  .branch([
    // Branch for high precipitation (indoor activities)
    [
      async ({ inputData }) => {
        return inputData?.precipitationChance > 50;
      },
      planIndoorActivities
    ],
    // Branch for low precipitation (outdoor activities)
    [
      async ({ inputData }) => {
        return inputData?.precipitationChance <= 50;
      },
      planActivities
    ]
  ]);

activityPlanningWorkflow.commit()

export { activityPlanningWorkflow }
```

----------------------------------------

TITLE: Complete Document Processing and Embedding Pipeline | Mastra & AI SDK | TypeScript
DESCRIPTION: Illustrates the end-to-end process of loading text into a MDocument, chunking it, generating embeddings using either OpenAI or Cohere via `embedMany`, and preparing them for storage in a vector database. Shows necessary imports and sequential steps.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/rag/chunking-and-embedding.mdx#_snippet_6

LANGUAGE: TypeScript
CODE:
```
import { embedMany } from "ai";
import { openai } from "@ai-sdk/openai";
import { cohere } from "@ai-sdk/cohere";

import { MDocument } from "@mastra/rag";

// Initialize document
const doc = MDocument.fromText(`
  Climate change poses significant challenges to global agriculture.
  Rising temperatures and changing precipitation patterns affect crop yields.
`);

// Create chunks
const chunks = await doc.chunk({
  strategy: "recursive",
  size: 256,
  overlap: 50,
});

// Generate embeddings with OpenAI
const { embeddings: openAIEmbeddings } = await embedMany({
  model: openai.embedding("text-embedding-3-small"),
  values: chunks.map((chunk) => chunk.text),
});

// OR

// Generate embeddings with Cohere
const { embeddings: cohereEmbeddings } = await embedMany({
  model: cohere.embedding("embed-english-v3.0"),
  values: chunks.map((chunk) => chunk.text),
});

// Store embeddings in your vector database
await vectorStore.upsert({
  indexName: "embeddings",
  vectors: embeddings,
});
```

----------------------------------------

TITLE: Defining a Custom Tool for Mastra Agents
DESCRIPTION: This snippet shows how to create a custom tool using `createTool` from `@mastra/core/tools`. Tools allow agents to interact with external systems. Each tool requires an ID, an input schema (using Zod), a description, and an `execute` function for its implementation.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/packages/core/README.md#_snippet_4

LANGUAGE: typescript
CODE:
```
import { createTool } from '@mastra/core/tools';
import { z } from 'zod';

const weatherInfo = createTool({
  id: 'Get Weather Information',
  inputSchema: z.object({
    city: z.string(),
  }),
  description: 'Fetches the current weather information for a given city',
  execute: async ({ context: { city } }) => {
    // Tool implementation
  },
});
```

----------------------------------------

TITLE: Creating a Thread with Memory (TypeScript)
DESCRIPTION: This snippet demonstrates how to initialize the Memory class and use the `createThread` method to create a new conversation thread, providing a resource ID, title, and optional metadata.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/reference/memory/createThread.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
import { Memory } from "@mastra/memory";

const memory = new Memory({
  /* config */
});
const thread = await memory.createThread({
  resourceId: "user-123",
  title: "Support Conversation",
  metadata: {
    category: "support",
    priority: "high",
  },
});
```

----------------------------------------

TITLE: Initializing MCPServer with Tools and Agents (TypeScript)
DESCRIPTION: This snippet demonstrates how to instantiate an `MCPServer` to expose Mastra tools, agents, and workflows. It shows the creation of a custom agent and a tool, then registers them along with a workflow to the server. Agents and workflows are automatically converted into callable tools for MCP clients.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/reference/tools/mcp-server.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { createTool } from "@mastra/core/tools";
import { MCPServer } from "@mastra/mcp";
import { z } from "zod";
import { dataProcessingWorkflow } from "../workflows/dataProcessingWorkflow";

const myAgent = new Agent({
  name: "MyExampleAgent",
  description: "A generalist to help with basic questions."
  instructions: "You are a helpful assistant.",
  model: openai("gpt-4o-mini"),
});

const weatherTool = createTool({
  id: "getWeather",
  description: "Gets the current weather for a location.",
  inputSchema: z.object({ location: z.string() }),
  execute: async ({ context }) => `Weather in ${context.location} is sunny.`,
});

const server = new MCPServer({
  name: "My Custom Server",
  version: "1.0.0",
  tools: { weatherTool },
  agents: { myAgent }, // this agent will become tool "ask_myAgent"
  workflows: {
    dataProcessingWorkflow, // this workflow will become tool "run_dataProcessingWorkflow"
  }
});
```

----------------------------------------

TITLE: Generating Structured Output with Zod Schema in Mastra
DESCRIPTION: This snippet demonstrates how to use Zod to define a type-safe schema for structured output from a Mastra agent. The Zod schema is then passed to the `output` option of the agent's `.generate()` method, ensuring the response conforms to the defined structure and provides strong typing.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/agents/overview.mdx#_snippet_8

LANGUAGE: ts
CODE:
```
import { z } from "zod";

// Define the Zod schema
const schema = z.object({
  summary: z.string(),
  keywords: z.array(z.string()),
});

// Use the schema with the agent
const response = await myAgent.generate(
  [
    {
      role: "user",
      content:
        "Please provide a summary and keywords for the following text: ...",
    },
  ],
  {
    output: schema,
  },
);

console.log("Structured Output:", response.object);
```

----------------------------------------

TITLE: Combining Multiple Branch Merges in Mastra Workflow (TypeScript)
DESCRIPTION: This snippet illustrates how to create highly complex dependency patterns by combining multiple '.after([])' calls. It shows three independent branches ('stepA'->'stepB'->'stepC', 'stepD'->'stepE', 'stepF'->'stepG') converging into a 'finalStep' which only executes after all three branch-ending steps ('stepC', 'stepE', 'stepG') have completed.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/ja/docs/workflows/control-flow.mdx#_snippet_4

LANGUAGE: TypeScript
CODE:
```
myWorkflow
  // First branch
  .step(stepA)
  .then(stepB)
  .then(stepC)

  // Second branch
  .step(stepD)
  .then(stepE)

  // Third branch
  .step(stepF)
  .then(stepG)

  // This step depends on the completion of multiple branches
  .after([stepC, stepE, stepG])
  .step(finalStep);
```

----------------------------------------

TITLE: Configuring and Committing the Mastra RAG Workflow Steps
DESCRIPTION: This snippet demonstrates how to sequentially chain all the defined workflow steps (`analyzeContext`, `breakdownThoughts`, `connectPieces`, `drawConclusions`, `finalAnswer`) using the `.step()` and `.then()` methods. Finally, `ragWorkflow.commit()` finalizes the workflow's configuration, making it ready for execution.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/rag/usage/cot-workflow-rag.mdx#_snippet_10

LANGUAGE: typescript
CODE:
```
ragWorkflow
  .step(analyzeContext)
  .then(breakdownThoughts)
  .then(connectPieces)
  .then(drawConclusions)
  .then(finalAnswer);

ragWorkflow.commit();
```

----------------------------------------

TITLE: Chunking and Embedding Initial Documents (TypeScript)
DESCRIPTION: This snippet demonstrates the process of chunking an initial document (`doc`) into smaller segments. It then generates embeddings for these chunks using OpenAI's 'text-embedding-3-small' model and upserts them into the 'embeddings' index of the PgVector store.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/ja/examples/rag/usage/cleanup-rag.mdx#_snippet_6

LANGUAGE: TypeScript
CODE:
```
const chunks = await doc.chunk({
  strategy: "recursive",
  size: 256,
  overlap: 50,
  separator: "\n",
});

const { embeddings } = await embedMany({
  model: openai.embedding("text-embedding-3-small"),
  values: chunks.map((chunk) => chunk.text),
});

const vectorStore = mastra.getVector("pgVector");
await vectorStore.createIndex({
  indexName: "embeddings",
  dimension: 1536,
});

await vectorStore.upsert({
  indexName: "embeddings",
  vectors: embeddings,
  metadata: chunks?.map((chunk: any) => ({ text: chunk.text })),
});
```

----------------------------------------

TITLE: Defining `fetchWeather` Workflow Step (TypeScript)
DESCRIPTION: This step, `fetchWeather`, is responsible for simulating the retrieval of weather information for a given city. It uses `zod` to define its input schema (city: string) and output schema (temperature: number, conditions: string, city: string), and its `execute` function returns mock weather data. This step serves as the initial data source for the workflow.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/workflows/using-with-agents-and-tools.mdx#_snippet_3

LANGUAGE: TypeScript
CODE:
```
const fetchWeather = createStep({
  id: "fetch-weather",
  inputSchema: z.object({
    city: z.string().describe("The city to get the weather for")
  }),
  outputSchema: z.object({
    temperature: z.number(),
    conditions: z.string(),
    city: z.string()
  }),
  execute: async ({ inputData }) => {
    return {
      temperature: 25,
      conditions: "Sunny",
      city: inputData.city
    };
  }
});
```

----------------------------------------

TITLE: Integrating Mastra Agents as Workflow Steps (TypeScript)
DESCRIPTION: This snippet demonstrates how to directly use a Mastra Agent as a step within a vNext workflow. It sets up an agent, defines a data preparation step, and then orchestrates them into a simple Q&A workflow, showcasing input mapping and execution.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/ja/docs/workflows-vnext/using-with-agents-and-tools.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { Mastra } from "@mastra/core";
import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { createWorkflow, createStep } from "@mastra/core/workflows/vNext";
import { z } from "zod";

const myAgent = new Agent({
  name: "myAgent",
  instructions: "You are a helpful assistant that answers questions concisely.",
  model: openai("gpt-4o"),
});

// Input preparation step
const preparationStep = createStep({
  id: "preparation",
  inputSchema: z.object({
      question: z.string()
  }),
  outputSchema: z.object({
      formattedPrompt: z.string()
  }),
  execute: async ({ inputData }) => {
      return {
          formattedPrompt: `Answer this question briefly: ${inputData.question}`
      };
  }
});

const agentStep = createStep(myAgent)

// Create a simple workflow
const myWorkflow = createWorkflow({
  id: "simple-qa-workflow",
  inputSchema: z.object({
      question: z.string()
  }),
  outputSchema: z.string(),
  steps: [preparationStep, agentStep]
});

// Define workflow sequence
myWorkflow
  .then(preparationStep)
  .map({
    prompt: {
        step: preparationStep,
        path: "formattedPrompt",
    },
  })
  .then(agentStep)
  .commit();

// Create Mastra instance
const mastra = new Mastra({
  agents: {
      myAgent,
  },
  vnext_workflows: {
      myWorkflow,
  },
});

const workflow = mastra.vnext_getWorkflow("myWorkflow");
const run = workflow.createRun();

// Run the workflow with a question
const res = await run.start({
  inputData: {
      question: "What is machine learning?"
  }
});

if (res.status === "success") {
  console.log("Answer:", res.result);
} else if (res.status === "failed") {
  console.error("Workflow failed:", res.error);
}
```

----------------------------------------

TITLE: Executing Parallel Nested Workflows in Mastra (TypeScript)
DESCRIPTION: This example illustrates how to run entire nested workflows in parallel. The outputs from these parallel workflows are collected into a single object, where keys correspond to the workflow IDs and values are their respective outputs, which then serve as input for the subsequent step.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/ja/docs/workflows-vnext/flow-control.mdx#_snippet_2

LANGUAGE: typescript
CODE:
```
myWorkflow
  .parallel([nestedWorkflow1, nestedWorkflow2])
  .then(finalStep)
  .commit();
```

----------------------------------------

TITLE: Defining and Initializing a Mastra Workflow - TypeScript
DESCRIPTION: This snippet shows how to define a workflow using `createWorkflow`, specifying its ID, input/output schemas, and an array of declared steps for type safety. It then demonstrates how to register this workflow with a `Mastra` instance and create a new run for execution.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/packages/core/src/workflows/README.md#_snippet_2

LANGUAGE: typescript
CODE:
```
const myWorkflow = createWorkflow({
  id: 'my-workflow',
  inputSchema: z.object({
    startValue: z.string(),
  }),
  outputSchema: z.object({
    result: z.string(),
  }),
  steps: [step1, step2, step3], // Declare steps used in this workflow
});

const mastra = new Mastra({
  vnext_workflows: {
    myWorkflow,
  },
});

const run = mastra.vnext_getWorkflow('myWorkflow').createRun();
```

----------------------------------------

TITLE: Re-ranking Results with Mastra RAG and OpenAI (TypeScript)
DESCRIPTION: This snippet demonstrates how to re-rank initial vector search results using the `@mastra/rag` library and an OpenAI model. It takes `initialResults` and a `query` to apply more sophisticated relevance scoring, improving retrieval quality. Ensure `metadata.text` is present in results for semantic scoring.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/rag/retrieval.mdx#_snippet_17

LANGUAGE: TypeScript
CODE:
```
import { openai } from "@ai-sdk/openai";
import { rerank } from "@mastra/rag";

// Get initial results from vector search
const initialResults = await pgVector.query({
  indexName: "embeddings",
  queryVector: queryEmbedding,
  topK: 10,
});

// Re-rank the results
const rerankedResults = await rerank(
  initialResults,
  query,
  openai("gpt-4o-mini"),
);
```

----------------------------------------

TITLE: Nesting Workflows in Mastra for Modular Design (TypeScript)
DESCRIPTION: This example illustrates how to nest one workflow (`nestedWorkflow`) as a step within another (`mainWorkflow`). For proper integration, the `nestedWorkflow`'s input schema must match the `initialStep`'s output, and its output schema must match the `finalStep`'s input, promoting modular and reusable workflow components.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/ja/docs/workflows-vnext/flow-control.mdx#_snippet_9

LANGUAGE: typescript
CODE:
```
const nestedWorkflow = createWorkflow({
  id: 'nested-workflow',
  inputSchema: z.object({...}),
  outputSchema: z.object({...})
})
  .then(step1)
  .then(step2)
  .commit();

const mainWorkflow = createWorkflow({
  id: 'main-workflow',
  inputSchema: z.object({...}),
  outputSchema: z.object({...})
})
  .then(initialStep)
  .then(nestedWorkflow)
  .then(finalStep)
  .commit();
```

----------------------------------------

TITLE: Implementing Foreach Loops in Mastra Workflows (TypeScript)
DESCRIPTION: This comprehensive example demonstrates the `foreach` step, which iterates over an array-typed input, executing a specified step for each item sequentially. It includes the definition of `mapStep` and `finalStep`, the `counterWorkflow` setup, and a full example of running the workflow and handling its results.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/ja/docs/workflows-vnext/flow-control.mdx#_snippet_7

LANGUAGE: typescript
CODE:
```
const mapStep = createStep({
  id: "map",
  description: "Maps (+11) on the current value",
  inputSchema: z.object({
    value: z.number()
  }),
  outputSchema: z.object({
    value: z.number()
  }),
  execute: async ({ inputData }) => {
    return { value: inputData.value + 11 };
  }
});

const finalStep = createStep({
  id: "final",
  description: "Final step that prints the result",
  inputSchema: z.array(z.object({ value: z.number() })),
  outputSchema: z.object({
    finalValue: z.number()
  }),
  execute: async ({ inputData }) => {
    return { finalValue: inputData.reduce((acc, curr) => acc + curr.value, 0) };
  }
});

const counterWorkflow = createWorkflow({
  steps: [mapStep, finalStep],
  id: "counter-workflow",
  inputSchema: z.array(z.object({ value: z.number() })),
  outputSchema: z.object({
    finalValue: z.number()
  })
});

counterWorkflow.foreach(mapStep).then(finalStep).commit();

const run = counterWorkflow.createRun();
const result = await run.start({
  inputData: [{ value: 1 }, { value: 22 }, { value: 333 }]
});

if (result.status === "success") {
  console.log(result.result); // only exists if status is success
} else if (result.status === "failed") {
  console.error(result.error); // only exists if status is failed, this is an instance of Error
}
```

----------------------------------------

TITLE: Configuring MCPClient and Agent with Tools - TypeScript
DESCRIPTION: This comprehensive example demonstrates initializing `MCPClient` with multiple server configurations (e.g., 'stockPrice', 'weather') and then creating an `Agent` using `mcp.getTools()`. It also includes an asynchronous function `checkWeatherResource` to showcase fetching and reading weather resources, illustrating the integration of `resources.list()` and `resources.read()` within an application context.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/reference/tools/mcp-client.mdx#_snippet_19

LANGUAGE: typescript
CODE:
```
import { MCPClient } from "@mastra/mcp";
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";

const mcp = new MCPClient({
  servers: {
    stockPrice: {
      command: "npx",
      args: ["tsx", "stock-price.ts"],
      env: {
        API_KEY: "your-api-key"
      },
      log: (logMessage) => {
        console.log(`[${logMessage.level}] ${logMessage.message}`);
      }
    },
    weather: {
      url: new URL("http://localhost:8080/sse")
    }
  },
  timeout: 30000 // Global 30s timeout
});

// Create an agent with access to all tools
const agent = new Agent({
  name: "Multi-tool Agent",
  instructions: "You have access to multiple tool servers.",
  model: openai("gpt-4"),
  tools: await mcp.getTools()
});

// Example of using resource methods
async function checkWeatherResource() {
  try {
    const weatherResources = await mcp.resources.list();
    if (weatherResources.weather && weatherResources.weather.length > 0) {
      const currentWeatherURI = weatherResources.weather[0].uri;
      const weatherData = await mcp.resources.read('weather', currentWeatherURI);
      console.log('Weather data:', weatherData.contents[0].text);
    }
  } catch (error) {
    console.error("Error fetching weather resource:", error);
  }
}
checkWeatherResource();
```

----------------------------------------

TITLE: Retrieving Top-K Results from Pinecone using Mastra and OpenAI Embeddings (TypeScript)
DESCRIPTION: This snippet demonstrates the end-to-end process of embedding text content using OpenAI, upserting these embeddings into a Pinecone vector database, and then querying the database to retrieve the top-K most semantically similar results. It showcases the integration of text chunking, embedding generation, and vector database operations to find relevant information.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/rag/query/retrieve-results.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import { openai } from "@ai-sdk/openai";
import { PineconeVector } from "@mastra/pinecone";
import { MDocument } from "@mastra/rag";
import { embedMany } from "ai";

const doc = MDocument.fromText("Your text content...");

const chunks = await doc.chunk();

const { embeddings } = await embedMany({
  values: chunks.map((chunk) => chunk.text),
  model: openai.embedding("text-embedding-3-small"),
});

const pinecone = new PineconeVector({
  apiKey: "your-api-key",
});

await pinecone.createIndex({
  indexName: "test_index",
  dimension: 1536,
});

await pinecone.upsert({
  indexName: "test_index",
  vectors: embeddings,
  metadata: chunks?.map((chunk: any) => ({ text: chunk.text })),
});

const topK = 10;

const results = await pinecone.query({
  indexName: "test_index",
  queryVector: embeddings[0],
  topK,
});

console.log(results);
```

----------------------------------------

TITLE: Providing Runtime Context for Agent Generation (TypeScript)
DESCRIPTION: This example demonstrates how to create and populate a `RuntimeContext` object to dynamically supply parameters during agent execution. It sets various context variables such as vector store name, index name, `topK` results, a category filter, and graph traversal parameters, which are then passed to an agent's `generate` method to influence its behavior at runtime.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/reference/tools/graph-rag-tool.mdx#_snippet_5

LANGUAGE: TypeScript
CODE:
```
const runtimeContext = new RuntimeContext<{ vectorStoreName: string; indexName: string; topK: number; filter: any }>();
runtimeContext.set("vectorStoreName", "my-store");
runtimeContext.set("indexName", "my-index");
runtimeContext.set("topK", 5);
runtimeContext.set("filter", { "category": "docs" });
runtimeContext.set("randomWalkSteps", 100);
runtimeContext.set("restartProb", 0.15);

const response = await agent.generate("Find documentation from the knowledge base.", {
  runtimeContext,
});
```

----------------------------------------

TITLE: Configuring Memory with OpenAI API Embedder - TypeScript
DESCRIPTION: This example demonstrates how to configure the `Memory` class to use an OpenAI API-based embedding model for semantic recall. It imports `openai` from `@ai-sdk/openai` and sets the `embedder` property to an OpenAI embedding instance, specifying the desired model (`text-embedding-3-small`). This approach leverages cloud-based embedding services for scalability and ease of use.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/reference/memory/Memory.mdx#_snippet_5

LANGUAGE: typescript
CODE:
```
import { Memory } from "@mastra/memory";
import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";

const agent = new Agent({
  memory: new Memory({
    embedder: openai.embedding("text-embedding-3-small"),
  }),
});
```

----------------------------------------

TITLE: Initializing Voice Agent with Murf Voice in TypeScript
DESCRIPTION: This snippet demonstrates how to initialize a Mastra AI voice agent using the Murf Voice provider. It configures the agent with a GPT-4o model, generates a text response, converts the text to an audio stream using Murf's TTS, and then plays the audio.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/voice/overview.mdx#_snippet_10

LANGUAGE: typescript
CODE:
```
import { Agent } from '@mastra/core/agent';
import { openai } from '@ai-sdk/openai';
import { MurfVoice } from "@mastra/voice-murf";
import { playAudio } from "@mastra/node-audio";

const voiceAgent = new Agent({
  name: "Voice Agent",
  instructions: "You are a voice assistant that can help users with their tasks.",
  model: openai("gpt-4o"),
  voice: new MurfVoice(),
});

const { text } = await voiceAgent.generate('What color is the sky?');

// Convert text to speech to an Audio Stream
const audioStream = await voiceAgent.voice.speak(text, {
  speaker: "default" // Optional: specify a speaker
});

playAudio(audioStream);
```

----------------------------------------

TITLE: Defining a Branching Candidate Workflow in TypeScript
DESCRIPTION: This snippet defines a `candidateWorkflow` using `createWorkflow`, specifying its input and output schemas. It then implements branching logic using `.then()` and `.branch()` to conditionally execute `askAboutSpecialty` or `askAboutRole` based on the `isTechnical` property of the input data. The workflow is committed at the end.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/guides/guide/ai-recruiter.mdx#_snippet_5

LANGUAGE: TypeScript
CODE:
```
const candidateWorkflow = createWorkflow({
  id: "candidate-workflow",
  inputSchema: z.object({
    resumeText: z.string(),
  }),
  outputSchema: z.object({
    question: z.string(),
  }),
});

candidateWorkflow
  .then(gatherCandidateInfo)
  .branch([
    // Branch for technical candidates
    [
      async ({ inputData }) => {
        return inputData?.isTechnical;
      },
      askAboutSpecialty,
    ],
    // Branch for non-technical candidates
    [
      async ({ inputData }) => {
        return !inputData?.isTechnical;
      },
      askAboutRole,
    ],
  ]);

candidateWorkflow.commit();
```

----------------------------------------

TITLE: Initializing Mastra Memory with PostgreSQL and Agent in TypeScript
DESCRIPTION: This snippet demonstrates how to set up Mastra's memory system using PostgreSQL for storage and PgVector for vector search capabilities. It initializes a Memory instance with connection details and options for message history and semantic recall, then creates an Agent that leverages this memory for conversational context.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/memory/memory-with-pg.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
import { Memory } from "@mastra/memory";
import { PostgresStore, PgVector } from "@mastra/pg";
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";

// PostgreSQL connection details
const host = "localhost";
const port = 5432;
const user = "postgres";
const database = "postgres";
const password = "postgres";
const connectionString = `postgresql://${user}:${password}@${host}:${port}`;

// Initialize memory with PostgreSQL storage and vector search
const memory = new Memory({
  storage: new PostgresStore({
    host,
    port,
    user,
    database,
    password,
  }),
  vector: new PgVector({ connectionString }),
  options: {
    lastMessages: 10,
    semanticRecall: {
      topK: 3,
      messageRange: 2,
    },
  },
});

// Create an agent with memory capabilities
const chefAgent = new Agent({
  name: "chefAgent",
  instructions:
    "You are Michel, a practical and experienced home chef who helps people cook great meals with whatever ingredients they have available.",
  model: openai("gpt-4o-mini"),
  memory,
});
```

----------------------------------------

TITLE: Installing @mastra/core with npm
DESCRIPTION: This snippet demonstrates how to install the `@mastra/core` package using npm, the Node.js package manager. This is the first step to set up the Mastra framework in your project.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/packages/core/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @mastra/core
```

----------------------------------------

TITLE: Chunking Plain Text with Mastra RAG in TypeScript
DESCRIPTION: This snippet demonstrates how to use the `MDocument` class from the `@mastra/rag` library to split a plain text string into smaller chunks. It initializes a document from text content and then calls the `chunk()` method, which processes the text using default semantic chunking settings. The result is an array of text segments suitable for retrieval or analysis.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/rag/chunking/chunk-text.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { MDocument } from "@mastra/rag";

const doc = MDocument.fromText("Your plain text content...");

const chunks = await doc.chunk();
```

----------------------------------------

TITLE: Implementing Self-Verification Prompting in Mastra (TypeScript)
DESCRIPTION: This snippet illustrates how to enable self-verification in Mastra prompts, allowing the model to validate its own outputs against specified criteria. It combines `thinking` steps for initial generation with `verificationSteps` and `constraints` to ensure the generated code meets requirements and handles various scenarios, including test cases.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/explorations/prompt/examples.md#_snippet_3

LANGUAGE: typescript
CODE:
```
type CodeGenerationVars = {
  requirements: string;
  language: 'typescript' | 'python' | 'javascript';
  testCases?: string[];
};

const codeGenPrompt = createPrompt<CodeGenerationVars>('Generate code with verification', {
  persona: 'Senior Software Engineer',
  outputFormat: 'markdown',
})
  .text('Generate {{language}} code that meets these requirements:\n\n{{requirements}}')
  .thinking({
    steps: [
      'Analyze requirements',
      'Plan implementation approach',
      'Write initial code',
      'Add error handling',
      'Implement input validation',
    ],
  })
  .verificationSteps([
    'Check if implementation meets all requirements',
    'Run test cases: {{testCases}}',
    'Verify edge case handling',
    'Validate error handling',
    'Check input validation',
    'Assess code quality',
    'Identify potential improvements',
  ])
  .constraints([
    'Must handle all edge cases',
    'Include input validation',
    'Add error handling',
    'Follow {{language}} best practices',
  ]);

// Usage example
const implementation = codeGenPrompt.toString({
  requirements: 'Create a function that validates email addresses',
  language: 'typescript',
  testCases: ['valid email', 'missing @', 'multiple @', 'invalid domain'],
});
```

----------------------------------------

TITLE: Configuring Mastra RAG Agent
DESCRIPTION: This code defines the `ragAgent`, an instance of `Agent`, responsible for generating responses. It sets a descriptive `name`, provides `instructions` to guide its behavior (emphasizing context-based answers), specifies `gpt-4o-mini` as its underlying language `model`, and integrates the previously created `vectorQueryTool` for retrieval capabilities.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/examples/rag/rerank/rerank-rag.mdx#_snippet_3

LANGUAGE: typescript
CODE:
```
export const ragAgent = new Agent({
  name: "RAG Agent",
  instructions: `You are a helpful assistant that answers questions based on the provided context. Keep your answers concise and relevant.
    Important: When asked to answer a question, please base your answer only on the context provided in the tool. 
    If the context doesn't contain enough information to fully answer the question, please state that explicitly.`,
  model: openai("gpt-4o-mini"),
  tools: {
    vectorQueryTool,
  },
});
```

----------------------------------------

TITLE: Implementing Real-time Voice Interaction with OpenAIRealtimeVoice - TypeScript
DESCRIPTION: Demonstrates how to use the OpenAIRealtimeVoice class. It covers initializing the class with default or specific options, updating session configurations like VAD, connecting to the service, setting up event listeners for incoming audio and transcribed text, sending text for speech synthesis, streaming audio input for transcription, and disconnecting. Requires `@mastra/voice-openai-realtime` and `@mastra/node-audio`.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/reference/voice/openai-realtime.mdx#_snippet_0

LANGUAGE: typescript
CODE:
```
import { OpenAIRealtimeVoice } from "@mastra/voice-openai-realtime";
import { playAudio, getMicrophoneStream } from "@mastra/node-audio";

// Initialize with default configuration using environment variables
const voice = new OpenAIRealtimeVoice();

// Or initialize with specific configuration
const voiceWithConfig = new OpenAIRealtimeVoice({
  apiKey: "your-openai-api-key",
  model: "gpt-4o-mini-realtime-preview-2024-12-17",
  speaker: "alloy" // Default voice
});

voiceWithConfig.updateSession({
  turn_detection: {
    type: "server_vad",
    threshold: 0.6,
    silence_duration_ms: 1200
  }
});

// Establish connection
await voice.connect();

// Set up event listeners
voice.on("speaker", ({ audio }) => {
  // Handle audio data (Int16Array) pcm format by default
  playAudio(audio);
});

voice.on("writing", ({ text, role }) => {
  // Handle transcribed text
  console.log(`${role}: ${text}`);
});

// Convert text to speech
await voice.speak("Hello, how can I help you today?", {
  speaker: "echo" // Override default voice
});

// Process audio input
const microphoneStream = getMicrophoneStream();
await voice.send(microphoneStream);

// When done, disconnect
voice.connect();
```

----------------------------------------

TITLE: Configuring Mastra Instance with Inngest API Endpoint (TypeScript)
DESCRIPTION: This snippet configures the Mastra instance, registering 'incrementWorkflow' and setting up an Inngest API route. The 'inngestServe' function integrates Mastra workflows with Inngest, handling function creation, event handling, state persistence, and real-time monitoring via a publish-subscribe system. It requires '@mastra/core/mastra', '@mastra/inngest', and '@mastra/loggers' dependencies.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/docs/workflows/inngest-workflow.mdx#_snippet_4

LANGUAGE: typescript
CODE:
```
import { Mastra } from '@mastra/core/mastra'
import { serve as inngestServe } from '@mastra/inngest'
import { incrementWorkflow } from './workflows'
import { inngest } from './inngest'
import { PinoLogger } from '@mastra/loggers'


// Configure Mastra with the workflow and Inngest API endpoint
export const mastra = new Mastra({
  workflows: {
    incrementWorkflow
  },
  server: {
    // The server configuration is required to allow local docker container can connect to the mastra server
    host: '0.0.0.0',
    apiRoutes: [
      // This API route is used to register the Mastra workflow (inngest function) on the inngest server
      {
        path: '/api/inngest',
        method: 'ALL',
        createHandler: async ({ mastra }) => inngestServe({ mastra, inngest }),
        // The inngestServe function integrates Mastra workflows with Inngest by:
        // 1. Creating Inngest functions for each workflow with unique IDs (workflow.${workflowId})
        // 2. Setting up event handlers that:
        //    - Generate unique run IDs for each workflow execution
        //    - Create an InngestExecutionEngine to manage step execution
        //    - Handle workflow state persistence and real-time updates
        // 3. Establishing a publish-subscribe system for real-time monitoring
        //    through the workflow:${workflowId}:${runId} channel
      },
    ],
  },
  logger: new PinoLogger({
    name: 'Mastra',
    level: 'info',
  }),
})
```

----------------------------------------

TITLE: Structured Output Streaming with Agent and Thread Context
DESCRIPTION: This example illustrates using `myAgent.stream()` to generate structured output based on a defined JSON schema. It also demonstrates maintaining conversation context via `threadId` and using an `onFinish` callback. The snippet shows how to consume both the `textStream` for real-time updates and await the `object` promise for the final structured result.
SOURCE: https://github.com/mastra-ai/mastra/blob/main/docs/src/content/en/reference/agents/stream.mdx#_snippet_2

LANGUAGE: typescript
CODE:
```
const schema = {
  type: "object",
  properties: {
    summary: { type: "string" },
    nextSteps: { type: "array", items: { type: "string" } },
  },
  required: ["summary", "nextSteps"],
};

const response = await myAgent.stream("What should we do next?", {
  output: schema,
  threadId: "project-123",
  onFinish: (text) => console.log("Finished:", text),
});

for await (const chunk of response.textStream) {
  console.log(chunk);
}

const result = await response.object;
console.log("Final structured result:", result);
```